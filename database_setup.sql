-- إن<PERSON><PERSON><PERSON> قاعدة البيانات
CREATE DATABASE IF NOT EXISTS invoice_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE invoice_system;

-- جدول الفواتير الرئيسي
CREATE TABLE IF NOT EXISTS invoices (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    invoice_date DATE NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    customer_address TEXT,
    payment_method ENUM('نقدي', 'شيك') DEFAULT 'نقدي',
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    discount DECIMAL(10,2) DEFAULT 0.00,
    previous_balance DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    driver VA<PERSON>HA<PERSON>(255),
    accountant <PERSON><PERSON><PERSON><PERSON>(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_customer_name (customer_name),
    INDEX idx_invoice_date (invoice_date),
    INDEX idx_payment_method (payment_method)
);

-- جدول أصناف الفاتورة
CREATE TABLE IF NOT EXISTS invoice_items (
    id INT AUTO_INCREMENT PRIMARY KEY,
    invoice_id INT NOT NULL,
    item_name VARCHAR(255) NOT NULL,
    quantity DECIMAL(10,3) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE,
    INDEX idx_invoice_id (invoice_id),
    INDEX idx_item_name (item_name)
);

-- جدول العملاء (للمساعدة في الفلترة والبحث)
CREATE TABLE IF NOT EXISTS customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_customer_name (name)
);

-- جدول الأصناف (للمساعدة في الإكمال التلقائي)
CREATE TABLE IF NOT EXISTS products (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    default_price DECIMAL(10,2),
    unit VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_product_name (name)
);

-- إدراج بعض البيانات التجريبية
INSERT INTO customers (name, address, phone) VALUES 
('عميل تجريبي 1', 'العنوان التجريبي 1', '123456789'),
('عميل تجريبي 2', 'العنوان التجريبي 2', '987654321')
ON DUPLICATE KEY UPDATE name=name;

INSERT INTO products (name, default_price, unit) VALUES 
('منتج تجريبي 1', 100.00, 'قطعة'),
('منتج تجريبي 2', 250.50, 'كيلو'),
('منتج تجريبي 3', 75.25, 'متر')
ON DUPLICATE KEY UPDATE name=name;

-- إنشاء مستخدم قاعدة البيانات (اختياري)
-- CREATE USER 'invoice_user'@'localhost' IDENTIFIED BY 'invoice_password';
-- GRANT ALL PRIVILEGES ON invoice_system.* TO 'invoice_user'@'localhost';
-- FLUSH PRIVILEGES;
