<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

// التعامل مع طلبات OPTIONS (CORS)
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($method) {
        case 'POST':
            if ($action === 'create') {
                createInvoice();
            }
            break;
        case 'GET':
            if ($action === 'list') {
                getInvoices();
            } elseif ($action === 'get' && isset($_GET['id'])) {
                getInvoice($_GET['id']);
            } elseif ($action === 'search') {
                searchInvoices();
            }
            break;
        case 'PUT':
            if ($action === 'update' && isset($_GET['id'])) {
                updateInvoice($_GET['id']);
            }
            break;
        case 'DELETE':
            if ($action === 'delete' && isset($_GET['id'])) {
                deleteInvoice($_GET['id']);
            }
            break;
        default:
            http_response_code(405);
            echo json_encode(['error' => 'طريقة غير مدعومة']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في الخادم: ' . $e->getMessage()]);
}

/**
 * إنشاء فاتورة جديدة
 */
function createInvoice() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'بيانات غير صحيحة']);
        return;
    }

    try {
        $pdo = getDBConnection();
        $pdo->beginTransaction();

        // توليد رقم فاتورة تلقائي إذا لم يتم تمريره
        if (empty($input['invoice_number'])) {
            $input['invoice_number'] = generateInvoiceNumber($pdo);
        }

        // حساب المبلغ المتبقي (للأجل)
        $remainingAmount = 0;
        if ($input['payment_method'] === 'أجل') {
            $remainingAmount = $input['total_amount'];
        }

        // إدراج الفاتورة الرئيسية
        $sql = "INSERT INTO invoices (invoice_number, invoice_date, customer_name, customer_address,
                payment_method, subtotal, discount, previous_balance, total_amount, remaining_amount,
                cartons_count, weight_tons, driver, sales_rep, supplier, accountant)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $input['invoice_number'],
            $input['invoice_date'],
            $input['customer_name'],
            $input['customer_address'] ?? '',
            $input['payment_method'],
            $input['subtotal'],
            $input['discount'] ?? 0,
            $input['previous_balance'] ?? 0,
            $input['total_amount'],
            $remainingAmount,
            $input['cartons_count'] ?? 0,
            $input['weight_tons'] ?? 0,
            $input['driver'] ?? '',
            $input['sales_rep'] ?? '',
            $input['supplier'] ?? '',
            $input['accountant'] ?? ''
        ]);

        $invoiceId = $pdo->lastInsertId();

        // إدراج أصناف الفاتورة
        if (!empty($input['items'])) {
            $itemSql = "INSERT INTO invoice_items (invoice_id, item_name, quantity, unit_price, total_price) 
                        VALUES (?, ?, ?, ?, ?)";
            $itemStmt = $pdo->prepare($itemSql);

            foreach ($input['items'] as $item) {
                $itemStmt->execute([
                    $invoiceId,
                    $item['name'],
                    $item['quantity'],
                    $item['price'],
                    $item['total']
                ]);
            }
        }

        // إضافة العميل إلى جدول العملاء إذا لم يكن موجوداً
        $customerSql = "INSERT INTO customers (name, address, total_debt) VALUES (?, ?, 0)
                        ON DUPLICATE KEY UPDATE address = VALUES(address)";
        $customerStmt = $pdo->prepare($customerSql);
        $customerStmt->execute([$input['customer_name'], $input['customer_address'] ?? '']);

        // تحديث ديون العميل إذا كانت طريقة الدفع أجل
        if ($input['payment_method'] === 'أجل') {
            $debtSql = "UPDATE customers SET total_debt = total_debt + ? WHERE name = ?";
            $debtStmt = $pdo->prepare($debtSql);
            $debtStmt->execute([$input['total_amount'], $input['customer_name']]);
        }

        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ الفاتورة بنجاح',
            'invoice_id' => $invoiceId
        ]);

    } catch (Exception $e) {
        $pdo->rollback();
        throw $e;
    }
}

/**
 * الحصول على قائمة الفواتير مع الفلترة
 */
function getInvoices() {
    $page = (int)($_GET['page'] ?? 1);
    $limit = (int)($_GET['limit'] ?? 20);
    $offset = ($page - 1) * $limit;

    // بناء الاستعلام مع الفلاتر
    $where = [];
    $params = [];

    if (!empty($_GET['customer'])) {
        $where[] = "customer_name LIKE ?";
        $params[] = '%' . $_GET['customer'] . '%';
    }

    if (!empty($_GET['date_from'])) {
        $where[] = "invoice_date >= ?";
        $params[] = $_GET['date_from'];
    }

    if (!empty($_GET['date_to'])) {
        $where[] = "invoice_date <= ?";
        $params[] = $_GET['date_to'];
    }

    if (!empty($_GET['payment_method'])) {
        $where[] = "payment_method = ?";
        $params[] = $_GET['payment_method'];
    }

    if (!empty($_GET['min_amount'])) {
        $where[] = "total_amount >= ?";
        $params[] = $_GET['min_amount'];
    }

    if (!empty($_GET['max_amount'])) {
        $where[] = "total_amount <= ?";
        $params[] = $_GET['max_amount'];
    }

    $whereClause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';

    // استعلام العد الإجمالي
    $countSql = "SELECT COUNT(*) as total FROM invoices $whereClause";
    $totalCount = fetchOne($countSql, $params)['total'];

    // استعلام البيانات
    $sql = "SELECT * FROM invoices $whereClause ORDER BY created_at DESC LIMIT $limit OFFSET $offset";
    $invoices = fetchAll($sql, $params);

    echo json_encode([
        'success' => true,
        'data' => $invoices,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => ceil($totalCount / $limit),
            'total_records' => $totalCount,
            'per_page' => $limit
        ]
    ]);
}

/**
 * الحصول على فاتورة واحدة مع أصنافها
 */
function getInvoice($id) {
    $invoice = fetchOne("SELECT * FROM invoices WHERE id = ?", [$id]);
    
    if (!$invoice) {
        http_response_code(404);
        echo json_encode(['error' => 'الفاتورة غير موجودة']);
        return;
    }

    $items = fetchAll("SELECT * FROM invoice_items WHERE invoice_id = ?", [$id]);
    $invoice['items'] = $items;

    echo json_encode([
        'success' => true,
        'data' => $invoice
    ]);
}

/**
 * البحث في الفواتير
 */
function searchInvoices() {
    $search = $_GET['q'] ?? '';
    
    if (empty($search)) {
        echo json_encode(['success' => true, 'data' => []]);
        return;
    }

    $sql = "SELECT * FROM invoices 
            WHERE invoice_number LIKE ? 
            OR customer_name LIKE ? 
            OR driver LIKE ? 
            OR accountant LIKE ?
            ORDER BY created_at DESC 
            LIMIT 50";
    
    $searchTerm = '%' . $search . '%';
    $results = fetchAll($sql, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);

    echo json_encode([
        'success' => true,
        'data' => $results
    ]);
}

/**
 * تحديث فاتورة
 */
function updateInvoice($id) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        http_response_code(400);
        echo json_encode(['error' => 'بيانات غير صحيحة']);
        return;
    }

    try {
        $pdo = getDBConnection();
        $pdo->beginTransaction();

        // الحصول على الفاتورة القديمة لمقارنة التغييرات
        $oldInvoice = fetchOne("SELECT customer_name, total_amount, payment_method FROM invoices WHERE id = ?", [$id]);

        // حساب المبلغ المتبقي الجديد
        $remainingAmount = 0;
        if ($input['payment_method'] === 'أجل') {
            $remainingAmount = $input['total_amount'];
        }

        // تحديث الفاتورة الرئيسية
        $sql = "UPDATE invoices SET
                invoice_number = ?, invoice_date = ?, customer_name = ?, customer_address = ?,
                payment_method = ?, subtotal = ?, discount = ?, previous_balance = ?,
                total_amount = ?, remaining_amount = ?, cartons_count = ?, weight_tons = ?,
                driver = ?, sales_rep = ?, supplier = ?, accountant = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $input['invoice_number'],
            $input['invoice_date'],
            $input['customer_name'],
            $input['customer_address'] ?? '',
            $input['payment_method'],
            $input['subtotal'],
            $input['discount'] ?? 0,
            $input['previous_balance'] ?? 0,
            $input['total_amount'],
            $remainingAmount,
            $input['cartons_count'] ?? 0,
            $input['weight_tons'] ?? 0,
            $input['driver'] ?? '',
            $input['sales_rep'] ?? '',
            $input['supplier'] ?? '',
            $input['accountant'] ?? '',
            $id
        ]);

        // تحديث ديون العميل إذا تغيرت طريقة الدفع أو المبلغ
        if ($oldInvoice) {
            // إزالة الدين القديم إذا كان أجل
            if ($oldInvoice['payment_method'] === 'أجل') {
                executeQuery("UPDATE customers SET total_debt = total_debt - ? WHERE name = ?",
                    [$oldInvoice['total_amount'], $oldInvoice['customer_name']]);
            }

            // إضافة الدين الجديد إذا كان أجل
            if ($input['payment_method'] === 'أجل') {
                executeQuery("UPDATE customers SET total_debt = total_debt + ? WHERE name = ?",
                    [$input['total_amount'], $input['customer_name']]);
            }
        }

        // حذف الأصناف القديمة وإدراج الجديدة
        $pdo->prepare("DELETE FROM invoice_items WHERE invoice_id = ?")->execute([$id]);

        if (!empty($input['items'])) {
            $itemSql = "INSERT INTO invoice_items (invoice_id, item_name, quantity, unit_price, total_price) 
                        VALUES (?, ?, ?, ?, ?)";
            $itemStmt = $pdo->prepare($itemSql);

            foreach ($input['items'] as $item) {
                $itemStmt->execute([
                    $id,
                    $item['name'],
                    $item['quantity'],
                    $item['price'],
                    $item['total']
                ]);
            }
        }

        $pdo->commit();

        echo json_encode([
            'success' => true,
            'message' => 'تم تحديث الفاتورة بنجاح'
        ]);

    } catch (Exception $e) {
        $pdo->rollback();
        throw $e;
    }
}

/**
 * حذف فاتورة
 */
function deleteInvoice($id) {
    try {
        $pdo = getDBConnection();
        $pdo->beginTransaction();

        // الحصول على تفاصيل الفاتورة قبل الحذف
        $invoice = fetchOne("SELECT customer_name, total_amount, payment_method FROM invoices WHERE id = ?", [$id]);

        if ($invoice && $invoice['payment_method'] === 'أجل') {
            // تقليل ديون العميل
            $debtSql = "UPDATE customers SET total_debt = total_debt - ? WHERE name = ?";
            $debtStmt = $pdo->prepare($debtSql);
            $debtStmt->execute([$invoice['total_amount'], $invoice['customer_name']]);
        }

        // حذف الفاتورة
        $result = executeQuery("DELETE FROM invoices WHERE id = ?", [$id]);

        $pdo->commit();

        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'تم حذف الفاتورة بنجاح'
            ]);
        } else {
            http_response_code(500);
            echo json_encode(['error' => 'فشل في حذف الفاتورة']);
        }
    } catch (Exception $e) {
        $pdo->rollback();
        throw $e;
    }
}

/**
 * توليد رقم فاتورة تلقائي
 */
function generateInvoiceNumber($pdo) {
    // الحصول على آخر رقم فاتورة
    $lastNumber = fetchOne("SELECT setting_value FROM system_settings WHERE setting_key = 'last_invoice_number'");
    $nextNumber = intval($lastNumber['setting_value']) + 1;

    // تحديث آخر رقم فاتورة
    executeQuery("UPDATE system_settings SET setting_value = ? WHERE setting_key = 'last_invoice_number'", [$nextNumber]);

    // تنسيق رقم الفاتورة
    $year = date('Y');
    $formattedNumber = str_pad($nextNumber, 6, '0', STR_PAD_LEFT);

    return "INV-{$year}-{$formattedNumber}";
}

/**
 * الحصول على رقم فاتورة جديد
 */
if ($_SERVER['REQUEST_METHOD'] == 'GET' && $action === 'new_invoice_number') {
    try {
        $pdo = getDBConnection();
        $newNumber = generateInvoiceNumber($pdo);
        echo json_encode([
            'success' => true,
            'invoice_number' => $newNumber
        ]);
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode(['error' => 'خطأ في توليد رقم الفاتورة']);
    }
    exit;
}
?>
