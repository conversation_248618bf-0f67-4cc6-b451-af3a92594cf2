# نظام إدارة الفواتير - Invoice Management System

نظام شامل لإدارة الفواتير باستخدام PHP و MySQL مع واجهة مستخدم عربية متقدمة.

## المميزات

### الميزات الأساسية
- ✅ إنشاء وحفظ الفواتير في قاعدة بيانات MySQL
- ✅ **رقم فاتورة تلقائي** مع تنسيق INV-YYYY-XXXXXX
- ✅ عرض وتصفح جميع الفواتير
- ✅ البحث في الفواتير
- ✅ تصدير البيانات إلى Excel
- ✅ حذف وتعديل الفواتير
- ✅ **إدارة ديون العملاء** مع نظام الأجل
- ✅ **حقول إضافية**: عدد الكراتين، الأوزان، المندوب، المجهز

### الفلاتر المتقدمة
- 📅 فلترة حسب التاريخ (من - إلى)
- 👤 فلترة حسب اسم العميل
- 💳 فلترة حسب طريقة الدفع (نقداً / أجل)
- 💰 فلترة حسب المبلغ (حد أدنى وأقصى)
- 🚛 فلترة حسب السائق
- 👨‍💼 فلترة حسب المندوب والمجهز والمحاسب

### إدارة الديون
- 💳 **نظام الأجل**: تتبع تلقائي للديون عند اختيار "أجل"
- 📊 **صفحة ديون العملاء**: عرض جميع العملاء المدينين
- 💰 **سداد الديون**: تسجيل المدفوعات وتحديث الأرصدة
- 📈 **إحصائيات الديون**: إجمالي الديون وعدد العملاء المدينين

### الميزات المتقدمة
- 📊 إحصائيات شاملة للمبيعات والديون
- 🔍 إكمال تلقائي للحقول (العملاء، المندوبين، السائقين، إلخ)
- 📄 ترقيم الصفحات مع عرض محسن
- 📱 تصميم متجاوب (Responsive)
- 🎨 واجهة مستخدم عربية جميلة مع ألوان مميزة للحالات
- 🔢 **رقم فاتورة تلقائي** بتنسيق احترافي

## متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- خادم ويب (Apache/Nginx)
- مكتبة PDO PHP

## التثبيت

### 1. إعداد قاعدة البيانات

#### للمستخدمين الجدد:
```sql
-- تشغيل ملف database_setup.sql
mysql -u root -p < database_setup.sql
```

#### للمستخدمين الحاليين (تحديث النظام):
```sql
-- تشغيل ملف database_update.sql لإضافة الحقول الجديدة
mysql -u root -p < database_update.sql
```

أو استيراد الملف من phpMyAdmin.

### 2. إعداد الاتصال بقاعدة البيانات

قم بتعديل ملف `config/database.php`:

```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'invoice_system');
define('DB_USER', 'your_username');    // اسم المستخدم
define('DB_PASS', 'your_password');    // كلمة المرور
```

### 3. رفع الملفات

ارفع جميع الملفات إلى مجلد الخادم (مثل htdocs في XAMPP).

### 4. تشغيل النظام

افتح المتصفح وانتقل إلى:
```
http://localhost/invoice/invoice_system.html
```

## هيكل المشروع

```
invoice/
├── config/
│   └── database.php          # إعدادات قاعدة البيانات
├── api/
│   ├── invoice_operations.php # عمليات الفواتير
│   └── helper_data.php       # البيانات المساعدة والإحصائيات
├── database_setup.sql        # ملف إنشاء قاعدة البيانات
├── invoice_system.html       # الواجهة الرئيسية
└── README.md                 # هذا الملف
```

## كيفية الاستخدام

### إنشاء فاتورة جديدة

1. انقر على تبويب "إدخال فاتورة جديدة"
2. **رقم الفاتورة**: يتم توليده تلقائياً بتنسيق INV-YYYY-XXXXXX
3. املأ معلومات الفاتورة الأساسية:
   - تاريخ الفاتورة
   - اسم العميل (مع إكمال تلقائي)
   - طريقة الدفع: **نقداً** أو **أجل**
4. املأ الحقول الإضافية:
   - عدد الكراتين
   - الأوزان بالطن
   - المندوب
   - المجهز
5. أضف الأصناف مع الكميات والأسعار
6. سيتم حساب الإجماليات تلقائياً
7. إذا اخترت "أجل" ستظهر **صافي المبلغ المتبقي**
8. انقر "💾 حفظ الفاتورة ومسح البيانات"

**ملاحظات مهمة**:
- عند اختيار "أجل" سيتم إضافة المبلغ تلقائياً لديون العميل
- **بعد الحفظ بنجاح سيتم مسح جميع البيانات تلقائياً** وإعداد فاتورة جديدة
- يمكنك استخدام **Ctrl+S** لحفظ الفاتورة بسرعة
- يمكنك استخدام **Ctrl+N** لمسح البيانات يدوياً
- استخدم **Enter** للانتقال بين الحقول بسرعة

### عرض وفلترة البيانات

1. انقر على تبويب "عرض البيانات"
2. استخدم الفلاتر المتقدمة للبحث:
   - حدد نطاق التاريخ
   - اكتب اسم العميل
   - اختر طريقة الدفع
   - حدد نطاق المبلغ
3. انقر "تطبيق الفلاتر"

### عرض الإحصائيات

1. في صفحة عرض البيانات، انقر "الإحصائيات"
2. ستظهر:
   - إجمالي الفواتير والمبيعات
   - مبيعات الشهر واليوم
   - أكثر العملاء شراءً
   - أكثر المنتجات مبيعاً

### إدارة ديون العملاء

1. انقر على تبويب "ديون العملاء"
2. ستظهر قائمة بجميع العملاء المدينين
3. يمكنك:
   - البحث عن عميل معين
   - عرض فواتير العميل (ينقلك لتبويب البيانات مع فلترة)
   - تسجيل سداد جزئي أو كامل
4. عند تسجيل السداد سيتم تحديث رصيد العميل تلقائياً

### اختصارات لوحة المفاتيح

لتسريع العمل، يمكنك استخدام الاختصارات التالية:

| الاختصار | الوظيفة |
|---------|---------|
| **Ctrl + S** | حفظ الفاتورة الحالية |
| **Ctrl + N** | مسح البيانات وإعداد فاتورة جديدة |
| **Enter** | الانتقال للحقل التالي |
| **Enter** في حقل الكمية/السعر | حساب إجمالي الصف |

### تصدير البيانات

انقر "تصدير البيانات" لتحميل ملف Excel يحتوي على جميع الفواتير مع الحقول الجديدة.

## API المتاحة

### عمليات الفواتير
- `POST api/invoice_operations.php?action=create` - إنشاء فاتورة
- `GET api/invoice_operations.php?action=list` - قائمة الفواتير
- `GET api/invoice_operations.php?action=get&id=X` - فاتورة محددة
- `GET api/invoice_operations.php?action=search&q=term` - البحث
- `DELETE api/invoice_operations.php?action=delete&id=X` - حذف فاتورة

### البيانات المساعدة
- `GET api/helper_data.php?action=stats` - الإحصائيات
- `GET api/helper_data.php?action=autocomplete&type=customer&q=term` - الإكمال التلقائي

## الأمان

- استخدام Prepared Statements لمنع SQL Injection
- تنظيف وتحقق من البيانات المدخلة
- استخدام HTTPS في الإنتاج
- تحديث كلمات مرور قاعدة البيانات بانتظام

## الدعم والتطوير

لأي استفسارات أو مشاكل، يرجى:
1. التأكد من إعدادات قاعدة البيانات
2. فحص ملفات الأخطاء في الخادم
3. التأكد من صلاحيات الملفات

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.
