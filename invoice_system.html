<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الفواتير</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .tab {
            flex: 1;
            padding: 15px;
            background: #e9ecef;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .tab.active {
            background: white;
            color: #667eea;
        }
        
        .tab:hover {
            background: #dee2e6;
        }
        
        .tab-content {
            display: none;
            padding: 30px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #495057;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .form-group {
            flex: 1;
            min-width: 200px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .items-table th, .items-table td {
            border: 1px solid #dee2e6;
            padding: 10px;
            text-align: center;
        }
        
        .items-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        
        .items-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .totals-section {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .total-row.final {
            font-weight: bold;
            font-size: 18px;
            color: #667eea;
            border-top: 2px solid #667eea;
            padding-top: 10px;
        }
        
        .file-input {
            margin-bottom: 20px;
            padding: 15px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .data-table th, .data-table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: right;
        }
        
        .data-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        
        .data-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .search-box {
            width: 100%;
            padding: 12px;
            margin-bottom: 20px;
            border: 2px solid #667eea;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .btn-sm {
            padding: 8px 15px;
            font-size: 14px;
        }

        .btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none !important;
        }

        .btn-success:disabled {
            background: #6c757d !important;
        }

        .form-actions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            border: 1px solid #dee2e6;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin: 20px 0;
        }

        .stat-card {
            transition: transform 0.3s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #667eea;
        }

        .no-data {
            text-align: center;
            padding: 40px;
            color: #6c757d;
        }

        .filter-active {
            background: #667eea !important;
            color: white !important;
        }

        .autocomplete-suggestions {
            position: absolute;
            background: white;
            border: 1px solid #ced4da;
            border-top: none;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            width: 100%;
            display: none;
        }

        .autocomplete-suggestion {
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
        }

        .autocomplete-suggestion:hover {
            background: #f8f9fa;
        }

        .autocomplete-suggestion.active {
            background: #667eea;
            color: white;
        }

        .form-group {
            position: relative;
        }

        .payment-method.credit {
            background: #fff3cd;
            color: #856404;
            padding: 3px 8px;
            border-radius: 4px;
            font-weight: bold;
        }

        .payment-method.cash {
            background: #d4edda;
            color: #155724;
            padding: 3px 8px;
            border-radius: 4px;
            font-weight: bold;
        }

        .remaining-amount.has-debt {
            background: #f8d7da;
            color: #721c24;
            padding: 3px 8px;
            border-radius: 4px;
            font-weight: bold;
        }

        .debt-amount {
            font-weight: bold;
            color: #dc3545;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            overflow-x: auto;
        }

        .tab {
            flex: 1;
            min-width: 150px;
            padding: 15px;
            background: #e9ecef;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s;
            white-space: nowrap;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
            }

            .container {
                margin: 10px;
                border-radius: 5px;
            }

            .data-table {
                font-size: 12px;
            }

            .btn {
                width: 100%;
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>نظام إدارة الفواتير</h1>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('invoice')">إدخال فاتورة جديدة</button>
            <button class="tab" onclick="showTab('data')">عرض البيانات</button>
            <button class="tab" onclick="showTab('debts')">ديون العملاء</button>
        </div>
        
        <!-- صفحة إدخال الفاتورة -->
        <div id="invoiceTab" class="tab-content active">
            <div class="alert alert-info" style="background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb;">
                <strong>نظام قاعدة البيانات:</strong> يتم حفظ البيانات تلقائياً في قاعدة بيانات MySQL<br>
                <small><strong>اختصارات لوحة المفاتيح:</strong> Ctrl+S للحفظ | Ctrl+N لمسح البيانات | Enter للانتقال للحقل التالي</small>
            </div>
            
            <form id="invoiceForm">
                <!-- معلومات الفاتورة الأساسية -->
                <div class="form-section">
                    <div class="section-title">معلومات الفاتورة</div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>رقم الفاتورة:</label>
                            <input type="text" id="invoiceNumber" required readonly style="background: #f8f9fa;">
                        </div>
                        <div class="form-group">
                            <label>تاريخ الفاتورة:</label>
                            <input type="date" id="invoiceDate" required>
                        </div>
                        <div class="form-group">
                            <label>اسم العميل:</label>
                            <input type="text" id="customerName" required autocomplete="off" onkeyup="showAutocomplete(this, 'customer')">
                            <div id="customerName_suggestions" class="autocomplete-suggestions"></div>
                        </div>
                        <div class="form-group">
                            <label>طريقة الدفع:</label>
                            <select id="paymentMethod" onchange="togglePaymentMethod()">
                                <option value="نقداً">نقداً</option>
                                <option value="أجل">أجل</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>عدد الكراتين:</label>
                            <input type="number" id="cartonsCount" min="0" value="0">
                        </div>
                        <div class="form-group">
                            <label>الأوزان بالطن:</label>
                            <input type="number" id="weightTons" step="0.001" min="0" value="0">
                        </div>
                        <div class="form-group">
                            <label>المندوب:</label>
                            <input type="text" id="salesRep" autocomplete="off" onkeyup="showAutocomplete(this, 'sales_rep')">
                            <div id="salesRep_suggestions" class="autocomplete-suggestions"></div>
                        </div>
                        <div class="form-group">
                            <label>المجهز:</label>
                            <input type="text" id="supplier" autocomplete="off" onkeyup="showAutocomplete(this, 'supplier')">
                            <div id="supplier_suggestions" class="autocomplete-suggestions"></div>
                        </div>
                    </div>
                </div>
                
                <!-- الأصناف -->
                <div class="form-section">
                    <div class="section-title">الأصناف</div>
                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>التسلسل</th>
                                <th>اسم الصنف</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>الإجمالي</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="itemsTableBody">
                            <tr>
                                <td>1</td>
                                <td><input type="text" class="item-name" placeholder="اسم الصنف"></td>
                                <td><input type="number" class="item-quantity" placeholder="الكمية" onchange="calculateRowTotal(this)"></td>
                                <td><input type="number" step="0.01" class="item-price" placeholder="السعر" onchange="calculateRowTotal(this)"></td>
                                <td class="item-total">0</td>
                                <td><button type="button" class="btn btn-danger" onclick="removeRow(this)">حذف</button></td>
                            </tr>
                        </tbody>
                    </table>
                    <button type="button" class="btn" onclick="addRow()" style="margin-top: 10px;">إضافة صنف</button>
                </div>
                
                <!-- الإجماليات -->
                <div class="form-section">
                    <div class="section-title">الإجماليات</div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>إجمالي الفاتورة:</label>
                            <input type="number" id="totalAmount" step="0.01" readonly>
                        </div>
                        <div class="form-group">
                            <label>الخصم:</label>
                            <input type="number" id="discount" step="0.01" value="0" onchange="calculateFinalTotal()">
                        </div>
                        <div class="form-group">
                            <label>الرصيد السابق:</label>
                            <input type="number" id="previousBalance" step="0.01" value="0" onchange="calculateFinalTotal()">
                        </div>
                    </div>
                    <div class="totals-section">
                        <div class="total-row final">
                            <span>صافي المبلغ المطلوب:</span>
                            <span id="finalTotal">0</span>
                        </div>
                        <div class="total-row" id="remainingAmountRow" style="display: none; color: #dc3545; font-weight: bold;">
                            <span>صافي المبلغ المتبقي (أجل):</span>
                            <span id="remainingAmount">0</span>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات إضافية -->
                <div class="form-section">
                    <div class="section-title">معلومات إضافية</div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>عنوان العميل:</label>
                            <textarea id="customerAddress" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label>السائق:</label>
                            <input type="text" id="driver">
                        </div>
                        <div class="form-group">
                            <label>المحاسب:</label>
                            <input type="text" id="accountant">
                        </div>
                    </div>
                </div>
                
                <div class="form-actions">
                    <div class="form-row">
                        <div class="form-group" style="flex: 2;">
                            <button type="submit" class="btn btn-success" style="width: 100%; font-size: 18px; padding: 15px;" id="saveInvoiceBtn">
                                💾 حفظ الفاتورة ومسح البيانات
                            </button>
                            <small style="color: #6c757d; display: block; text-align: center; margin-top: 5px;">
                                اختصار: Ctrl + S
                            </small>
                        </div>
                        <div class="form-group" style="flex: 1;">
                            <button type="button" class="btn btn-secondary" style="width: 100%; font-size: 16px; padding: 15px;" onclick="clearFormManually()">
                                🗑️ مسح البيانات
                            </button>
                            <small style="color: #6c757d; display: block; text-align: center; margin-top: 5px;">
                                اختصار: Ctrl + N
                            </small>
                        </div>
                    </div>
                </div>
            </form>
        </div>
        
        <!-- صفحة عرض البيانات -->
        <div id="dataTab" class="tab-content">
            <!-- البحث والفلاتر -->
            <div class="form-section">
                <div class="section-title">البحث والفلاتر</div>

                <!-- البحث العام -->
                <div class="form-row">
                    <div class="form-group">
                        <label>البحث العام:</label>
                        <input type="text" id="searchBox" placeholder="ابحث في الفواتير..." onkeyup="filterData()">
                    </div>
                </div>

                <!-- الفلاتر المتقدمة -->
                <div class="form-row">
                    <div class="form-group">
                        <label>من تاريخ:</label>
                        <input type="date" id="dateFrom" onchange="applyFilters()">
                    </div>
                    <div class="form-group">
                        <label>إلى تاريخ:</label>
                        <input type="date" id="dateTo" onchange="applyFilters()">
                    </div>
                    <div class="form-group">
                        <label>اسم العميل:</label>
                        <input type="text" id="customerFilter" placeholder="اسم العميل" onkeyup="applyFilters()">
                    </div>
                    <div class="form-group">
                        <label>طريقة الدفع:</label>
                        <select id="paymentFilter" onchange="applyFilters()">
                            <option value="">جميع طرق الدفع</option>
                            <option value="نقداً">نقداً</option>
                            <option value="أجل">أجل</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label>الحد الأدنى للمبلغ:</label>
                        <input type="number" id="minAmount" placeholder="0" onchange="applyFilters()">
                    </div>
                    <div class="form-group">
                        <label>الحد الأقصى للمبلغ:</label>
                        <input type="number" id="maxAmount" placeholder="بدون حد" onchange="applyFilters()">
                    </div>
                    <div class="form-group">
                        <label>السائق:</label>
                        <input type="text" id="driverFilter" placeholder="اسم السائق" onkeyup="applyFilters()">
                    </div>
                    <div class="form-group">
                        <label>المندوب:</label>
                        <input type="text" id="salesRepFilter" placeholder="اسم المندوب" onkeyup="applyFilters()">
                    </div>
                    <div class="form-group">
                        <label>المجهز:</label>
                        <input type="text" id="supplierFilter" placeholder="اسم المجهز" onkeyup="applyFilters()">
                    </div>
                    <div class="form-group">
                        <label>المحاسب:</label>
                        <input type="text" id="accountantFilter" placeholder="اسم المحاسب" onkeyup="applyFilters()">
                    </div>
                </div>

                <!-- أزرار التحكم -->
                <div class="form-row">
                    <button class="btn" onclick="applyFilters()">تطبيق الفلاتر</button>
                    <button class="btn btn-secondary" onclick="clearFilters()">مسح الفلاتر</button>
                    <button class="btn btn-secondary" onclick="exportData()">تصدير البيانات</button>
                    <button class="btn btn-secondary" onclick="showStatistics()">الإحصائيات</button>
                </div>
            </div>

            <!-- عرض الإحصائيات -->
            <div id="statisticsSection" class="form-section" style="display: none;">
                <div class="section-title">الإحصائيات</div>
                <div id="statisticsDisplay"></div>
            </div>

            <!-- عرض البيانات -->
            <div id="dataDisplay">
                <p>لا توجد بيانات للعرض. قم بإنشاء فاتورة جديدة.</p>
            </div>
        </div>

        <!-- صفحة ديون العملاء -->
        <div id="debtsTab" class="tab-content">
            <div class="form-section">
                <div class="section-title">ديون العملاء</div>
                <div class="form-row">
                    <div class="form-group">
                        <label>البحث في العملاء:</label>
                        <input type="text" id="debtSearchBox" placeholder="ابحث عن عميل..." onkeyup="filterDebts()">
                    </div>
                    <div class="form-group">
                        <button class="btn" onclick="loadDebts()">تحديث البيانات</button>
                        <button class="btn btn-secondary" onclick="exportDebts()">تصدير الديون</button>
                    </div>
                </div>
            </div>

            <!-- عرض ديون العملاء -->
            <div id="debtsDisplay">
                <p>لا توجد ديون للعرض.</p>
            </div>
        </div>
    </div>
    
    <script>
        let invoiceData = [];
        let rowCounter = 1;

        // تبديل التبويبات
        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });

            document.getElementById(tabName + 'Tab').classList.add('active');
            event.target.classList.add('active');

            if (tabName === 'data') {
                loadData();
            } else if (tabName === 'debts') {
                loadDebts();
            }
        }

        // تبديل طريقة الدفع
        function togglePaymentMethod() {
            const paymentMethod = document.getElementById('paymentMethod').value;
            const remainingAmountRow = document.getElementById('remainingAmountRow');

            if (paymentMethod === 'أجل') {
                remainingAmountRow.style.display = 'flex';
                updateRemainingAmount();
            } else {
                remainingAmountRow.style.display = 'none';
                document.getElementById('remainingAmount').textContent = '0';
            }
        }

        // تحديث المبلغ المتبقي
        function updateRemainingAmount() {
            const paymentMethod = document.getElementById('paymentMethod').value;
            const finalTotal = parseFloat(document.getElementById('finalTotal').textContent) || 0;

            if (paymentMethod === 'أجل') {
                document.getElementById('remainingAmount').textContent = finalTotal.toFixed(2);
            } else {
                document.getElementById('remainingAmount').textContent = '0';
            }
        }

        // مسح البيانات يدوياً
        function clearFormManually() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات المدخلة؟')) {
                resetForm();
                showAlert('تم مسح البيانات بنجاح', 'success');
            }
        }
        
        // إضافة صف جديد للأصناف
        function addRow() {
            rowCounter++;
            const tableBody = document.getElementById('itemsTableBody');
            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td>${rowCounter}</td>
                <td><input type="text" class="item-name" placeholder="اسم الصنف"></td>
                <td><input type="number" class="item-quantity" placeholder="الكمية" onchange="calculateRowTotal(this)"></td>
                <td><input type="number" step="0.01" class="item-price" placeholder="السعر" onchange="calculateRowTotal(this)"></td>
                <td class="item-total">0</td>
                <td><button type="button" class="btn btn-danger" onclick="removeRow(this)">حذف</button></td>
            `;
            tableBody.appendChild(newRow);
        }
        
        // حذف صف
        function removeRow(button) {
            const row = button.closest('tr');
            row.remove();
            calculateTotal();
            updateRowNumbers();
        }
        
        // تحديث أرقام الصفوف
        function updateRowNumbers() {
            const rows = document.querySelectorAll('#itemsTableBody tr');
            rows.forEach((row, index) => {
                row.firstElementChild.textContent = index + 1;
            });
            rowCounter = rows.length;
        }
        
        // حساب إجمالي الصف
        function calculateRowTotal(input) {
            const row = input.closest('tr');
            const quantity = row.querySelector('.item-quantity').value || 0;
            const price = row.querySelector('.item-price').value || 0;
            const total = quantity * price;
            
            row.querySelector('.item-total').textContent = total.toFixed(2);
            calculateTotal();
        }
        
        // حساب الإجمالي العام
        function calculateTotal() {
            let total = 0;
            document.querySelectorAll('.item-total').forEach(cell => {
                total += parseFloat(cell.textContent) || 0;
            });
            
            document.getElementById('totalAmount').value = total.toFixed(2);
            calculateFinalTotal();
        }
        
        // حساب الإجمالي النهائي
        function calculateFinalTotal() {
            const total = parseFloat(document.getElementById('totalAmount').value) || 0;
            const discount = parseFloat(document.getElementById('discount').value) || 0;
            const previousBalance = parseFloat(document.getElementById('previousBalance').value) || 0;

            const finalTotal = total - discount + previousBalance;
            document.getElementById('finalTotal').textContent = finalTotal.toFixed(2);

            // تحديث المبلغ المتبقي إذا كانت طريقة الدفع أجل
            updateRemainingAmount();
        }
        
        // حفظ الفاتورة
        document.getElementById('invoiceForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // التحقق من البيانات الأساسية
            const customerName = document.getElementById('customerName').value.trim();
            const invoiceDate = document.getElementById('invoiceDate').value;

            if (!customerName) {
                showAlert('يرجى إدخال اسم العميل', 'danger');
                document.getElementById('customerName').focus();
                return;
            }

            if (!invoiceDate) {
                showAlert('يرجى إدخال تاريخ الفاتورة', 'danger');
                document.getElementById('invoiceDate').focus();
                return;
            }

            // جمع بيانات الأصناف
            const items = [];
            let hasValidItems = false;

            document.querySelectorAll('#itemsTableBody tr').forEach(row => {
                const name = row.querySelector('.item-name').value.trim();
                const quantity = row.querySelector('.item-quantity').value;
                const price = row.querySelector('.item-price').value;
                const total = row.querySelector('.item-total').textContent;

                if (name && quantity && price && parseFloat(quantity) > 0 && parseFloat(price) > 0) {
                    items.push({
                        name: name,
                        quantity: parseFloat(quantity),
                        price: parseFloat(price),
                        total: parseFloat(total)
                    });
                    hasValidItems = true;
                }
            });

            if (!hasValidItems) {
                showAlert('يرجى إضافة صنف واحد على الأقل مع كمية وسعر صحيحين', 'danger');
                return;
            }

            // التحقق من الإجمالي
            const totalAmount = parseFloat(document.getElementById('finalTotal').textContent);
            if (totalAmount <= 0) {
                showAlert('إجمالي الفاتورة يجب أن يكون أكبر من صفر', 'danger');
                return;
            }

            // إنشاء كائن الفاتورة
            const invoice = {
                invoice_number: document.getElementById('invoiceNumber').value,
                invoice_date: invoiceDate,
                customer_name: customerName,
                customer_address: document.getElementById('customerAddress').value.trim(),
                payment_method: document.getElementById('paymentMethod').value,
                items: items,
                subtotal: parseFloat(document.getElementById('totalAmount').value) || 0,
                discount: parseFloat(document.getElementById('discount').value) || 0,
                previous_balance: parseFloat(document.getElementById('previousBalance').value) || 0,
                total_amount: totalAmount,
                cartons_count: parseInt(document.getElementById('cartonsCount').value) || 0,
                weight_tons: parseFloat(document.getElementById('weightTons').value) || 0,
                driver: document.getElementById('driver').value.trim(),
                sales_rep: document.getElementById('salesRep').value.trim(),
                supplier: document.getElementById('supplier').value.trim(),
                accountant: document.getElementById('accountant').value.trim()
            };

            // حفظ في قاعدة البيانات
            saveInvoiceToDatabase(invoice);
        });
        
        // حفظ الفاتورة في قاعدة البيانات
        async function saveInvoiceToDatabase(invoice) {
            const saveBtn = document.getElementById('saveInvoiceBtn');
            const originalText = saveBtn.textContent;

            try {
                // إظهار مؤشر التحميل
                saveBtn.disabled = true;
                saveBtn.textContent = 'جاري حفظ الفاتورة...';
                saveBtn.style.background = '#6c757d';

                const response = await fetch('api/invoice_operations.php?action=create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(invoice)
                });

                const result = await response.json();

                if (result.success) {
                    // إظهار رسالة النجاح
                    saveBtn.textContent = 'تم الحفظ بنجاح ✓';
                    saveBtn.style.background = '#28a745';

                    showAlert(`تم حفظ الفاتورة رقم ${invoice.invoice_number} بنجاح! سيتم مسح البيانات خلال ثانيتين...`, 'success');

                    // مسح البيانات بعد الحفظ بنجاح
                    setTimeout(() => {
                        resetForm();
                        showAlert('تم مسح البيانات وإعداد فاتورة جديدة', 'success');
                        // إعادة تعيين الزر
                        saveBtn.disabled = false;
                        saveBtn.textContent = originalText;
                        saveBtn.style.background = '#28a745';
                    }, 2000);

                    // تحديث البيانات في تبويب عرض البيانات إذا كان مفتوحاً
                    if (document.getElementById('dataTab').classList.contains('active')) {
                        loadData();
                    }
                } else {
                    throw new Error(result.error || 'خطأ غير معروف');
                }
            } catch (error) {
                console.error('Error:', error);
                showAlert('خطأ في حفظ الفاتورة: ' + error.message, 'danger');

                // إعادة تعيين الزر في حالة الخطأ
                saveBtn.disabled = false;
                saveBtn.textContent = originalText;
                saveBtn.style.background = '#28a745';
            }
        }
        
        // إعادة تعيين النموذج
        function resetForm() {
            // إعادة تعيين النموذج
            document.getElementById('invoiceForm').reset();

            // إعادة تعيين جدول الأصناف
            document.getElementById('itemsTableBody').innerHTML = `
                <tr>
                    <td>1</td>
                    <td><input type="text" class="item-name" placeholder="اسم الصنف"></td>
                    <td><input type="number" class="item-quantity" placeholder="الكمية" onchange="calculateRowTotal(this)"></td>
                    <td><input type="number" step="0.01" class="item-price" placeholder="السعر" onchange="calculateRowTotal(this)"></td>
                    <td class="item-total">0</td>
                    <td><button type="button" class="btn btn-danger" onclick="removeRow(this)">حذف</button></td>
                </tr>
            `;

            // إعادة تعيين العدادات والقيم
            rowCounter = 1;
            document.getElementById('totalAmount').value = '0';
            document.getElementById('finalTotal').textContent = '0';
            document.getElementById('remainingAmount').textContent = '0';
            document.getElementById('remainingAmountRow').style.display = 'none';

            // إعادة تعيين الحقول الجديدة
            document.getElementById('cartonsCount').value = '0';
            document.getElementById('weightTons').value = '0';
            document.getElementById('salesRep').value = '';
            document.getElementById('supplier').value = '';
            document.getElementById('driver').value = '';
            document.getElementById('accountant').value = '';
            document.getElementById('customerAddress').value = '';
            document.getElementById('discount').value = '0';
            document.getElementById('previousBalance').value = '0';

            // إعادة تعيين طريقة الدفع إلى نقداً
            document.getElementById('paymentMethod').value = 'نقداً';

            // إخفاء جميع اقتراحات الإكمال التلقائي
            document.querySelectorAll('.autocomplete-suggestions').forEach(div => {
                div.style.display = 'none';
            });

            // تعيين التاريخ الحالي
            document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];

            // توليد رقم فاتورة جديد
            generateInvoiceNumber();

            // التركيز على حقل اسم العميل
            setTimeout(() => {
                document.getElementById('customerName').focus();
            }, 100);
        }
        
        // تحميل البيانات لعرضها
        async function loadData() {
            const dataDisplay = document.getElementById('dataDisplay');

            try {
                // إظهار مؤشر التحميل
                dataDisplay.innerHTML = '<p>جاري تحميل البيانات...</p>';

                const response = await fetch('api/invoice_operations.php?action=list');
                const result = await response.json();

                if (result.success && result.data.length > 0) {
                    invoiceData = result.data;
                    displayInvoiceData(result.data);
                } else {
                    dataDisplay.innerHTML = '<p>لا توجد بيانات للعرض. قم بإنشاء فاتورة جديدة.</p>';
                }
            } catch (error) {
                console.error('Error loading data:', error);
                dataDisplay.innerHTML = '<p>خطأ في تحميل البيانات. تأكد من الاتصال بالخادم.</p>';
            }
        }

        // عرض بيانات الفواتير
        function displayInvoiceData(data) {
            const dataDisplay = document.getElementById('dataDisplay');

            let html = '<table class="data-table"><thead><tr>';
            html += '<th>رقم الفاتورة</th>';
            html += '<th>التاريخ</th>';
            html += '<th>اسم العميل</th>';
            html += '<th>طريقة الدفع</th>';
            html += '<th>إجمالي الفاتورة</th>';
            html += '<th>الخصم</th>';
            html += '<th>صافي المبلغ</th>';
            html += '<th>المبلغ المتبقي</th>';
            html += '<th>عدد الكراتين</th>';
            html += '<th>الوزن (طن)</th>';
            html += '<th>المندوب</th>';
            html += '<th>السائق</th>';
            html += '<th>إجراءات</th>';
            html += '</tr></thead><tbody>';

            data.forEach(invoice => {
                html += '<tr>';
                html += `<td>${invoice.invoice_number}</td>`;
                html += `<td>${invoice.invoice_date}</td>`;
                html += `<td>${invoice.customer_name}</td>`;
                html += `<td><span class="payment-method ${invoice.payment_method === 'أجل' ? 'credit' : 'cash'}">${invoice.payment_method}</span></td>`;
                html += `<td>${parseFloat(invoice.subtotal).toFixed(2)}</td>`;
                html += `<td>${parseFloat(invoice.discount).toFixed(2)}</td>`;
                html += `<td>${parseFloat(invoice.total_amount).toFixed(2)}</td>`;
                html += `<td><span class="remaining-amount ${invoice.remaining_amount > 0 ? 'has-debt' : ''}">${parseFloat(invoice.remaining_amount || 0).toFixed(2)}</span></td>`;
                html += `<td>${invoice.cartons_count || 0}</td>`;
                html += `<td>${parseFloat(invoice.weight_tons || 0).toFixed(3)}</td>`;
                html += `<td>${invoice.sales_rep || ''}</td>`;
                html += `<td>${invoice.driver || ''}</td>`;
                html += `<td>
                    <button class="btn btn-sm" onclick="viewInvoice(${invoice.id})">عرض</button>
                    <button class="btn btn-danger btn-sm" onclick="deleteInvoice(${invoice.id})">حذف</button>
                </td>`;
                html += '</tr>';
            });

            html += '</tbody></table>';
            dataDisplay.innerHTML = html;
        }
        
        // البحث في البيانات
        async function filterData() {
            const searchTerm = document.getElementById('searchBox').value;

            if (searchTerm.length < 2) {
                applyFilters();
                return;
            }

            try {
                const response = await fetch(`api/invoice_operations.php?action=search&q=${encodeURIComponent(searchTerm)}`);
                const result = await response.json();

                if (result.success) {
                    displayInvoiceData(result.data);
                } else {
                    document.getElementById('dataDisplay').innerHTML = '<p>خطأ في البحث</p>';
                }
            } catch (error) {
                console.error('Search error:', error);
                document.getElementById('dataDisplay').innerHTML = '<p>خطأ في البحث</p>';
            }
        }

        // تطبيق الفلاتر المتقدمة
        async function applyFilters() {
            const filters = {
                date_from: document.getElementById('dateFrom').value,
                date_to: document.getElementById('dateTo').value,
                customer: document.getElementById('customerFilter').value,
                payment_method: document.getElementById('paymentFilter').value,
                min_amount: document.getElementById('minAmount').value,
                max_amount: document.getElementById('maxAmount').value,
                driver: document.getElementById('driverFilter').value,
                sales_rep: document.getElementById('salesRepFilter').value,
                supplier: document.getElementById('supplierFilter').value,
                accountant: document.getElementById('accountantFilter').value
            };

            // إزالة الفلاتر الفارغة
            Object.keys(filters).forEach(key => {
                if (!filters[key]) {
                    delete filters[key];
                }
            });

            try {
                const queryString = new URLSearchParams(filters).toString();
                const response = await fetch(`api/invoice_operations.php?action=list&${queryString}`);
                const result = await response.json();

                if (result.success) {
                    displayInvoiceData(result.data);
                    updatePagination(result.pagination);
                } else {
                    document.getElementById('dataDisplay').innerHTML = '<p>خطأ في تطبيق الفلاتر</p>';
                }
            } catch (error) {
                console.error('Filter error:', error);
                document.getElementById('dataDisplay').innerHTML = '<p>خطأ في تطبيق الفلاتر</p>';
            }
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('dateFrom').value = '';
            document.getElementById('dateTo').value = '';
            document.getElementById('customerFilter').value = '';
            document.getElementById('paymentFilter').value = '';
            document.getElementById('minAmount').value = '';
            document.getElementById('maxAmount').value = '';
            document.getElementById('driverFilter').value = '';
            document.getElementById('salesRepFilter').value = '';
            document.getElementById('supplierFilter').value = '';
            document.getElementById('accountantFilter').value = '';
            document.getElementById('searchBox').value = '';

            loadData();
        }

        // تحديث ترقيم الصفحات
        function updatePagination(pagination) {
            if (!pagination) return;

            let paginationHtml = '<div class="pagination" style="margin-top: 20px; text-align: center;">';

            if (pagination.current_page > 1) {
                paginationHtml += `<button class="btn btn-sm" onclick="loadPage(${pagination.current_page - 1})">السابق</button>`;
            }

            paginationHtml += `<span style="margin: 0 15px;">صفحة ${pagination.current_page} من ${pagination.total_pages} (${pagination.total_records} سجل)</span>`;

            if (pagination.current_page < pagination.total_pages) {
                paginationHtml += `<button class="btn btn-sm" onclick="loadPage(${pagination.current_page + 1})">التالي</button>`;
            }

            paginationHtml += '</div>';

            document.getElementById('dataDisplay').innerHTML += paginationHtml;
        }

        // تحميل صفحة معينة
        async function loadPage(page) {
            try {
                const response = await fetch(`api/invoice_operations.php?action=list&page=${page}`);
                const result = await response.json();

                if (result.success) {
                    displayInvoiceData(result.data);
                    updatePagination(result.pagination);
                }
            } catch (error) {
                console.error('Page load error:', error);
            }
        }

        // عرض تفاصيل فاتورة
        async function viewInvoice(id) {
            try {
                const response = await fetch(`api/invoice_operations.php?action=get&id=${id}`);
                const result = await response.json();

                if (result.success) {
                    const invoice = result.data;
                    let details = `
                        <h3>تفاصيل الفاتورة رقم: ${invoice.invoice_number}</h3>
                        <p><strong>التاريخ:</strong> ${invoice.invoice_date}</p>
                        <p><strong>العميل:</strong> ${invoice.customer_name}</p>
                        <p><strong>العنوان:</strong> ${invoice.customer_address || 'غير محدد'}</p>
                        <p><strong>طريقة الدفع:</strong> ${invoice.payment_method}</p>
                        <p><strong>السائق:</strong> ${invoice.driver || 'غير محدد'}</p>
                        <p><strong>المحاسب:</strong> ${invoice.accountant || 'غير محدد'}</p>
                        <h4>الأصناف:</h4>
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>اسم الصنف</th>
                                    <th>الكمية</th>
                                    <th>السعر</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;

                    invoice.items.forEach(item => {
                        details += `
                            <tr>
                                <td>${item.item_name}</td>
                                <td>${item.quantity}</td>
                                <td>${item.unit_price}</td>
                                <td>${item.total_price}</td>
                            </tr>
                        `;
                    });

                    details += `
                            </tbody>
                        </table>
                        <p><strong>المجموع الفرعي:</strong> ${invoice.subtotal}</p>
                        <p><strong>الخصم:</strong> ${invoice.discount}</p>
                        <p><strong>الرصيد السابق:</strong> ${invoice.previous_balance}</p>
                        <p><strong>الإجمالي النهائي:</strong> ${invoice.total_amount}</p>
                        <button class="btn" onclick="loadData()">العودة للقائمة</button>
                    `;

                    document.getElementById('dataDisplay').innerHTML = details;
                } else {
                    showAlert('خطأ في تحميل تفاصيل الفاتورة', 'danger');
                }
            } catch (error) {
                console.error('Error:', error);
                showAlert('خطأ في الاتصال بالخادم', 'danger');
            }
        }

        // حذف فاتورة
        async function deleteInvoice(id) {
            if (!confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
                return;
            }

            try {
                const response = await fetch(`api/invoice_operations.php?action=delete&id=${id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showAlert('تم حذف الفاتورة بنجاح', 'success');
                    loadData();
                } else {
                    showAlert('خطأ في حذف الفاتورة', 'danger');
                }
            } catch (error) {
                console.error('Error:', error);
                showAlert('خطأ في الاتصال بالخادم', 'danger');
            }
        }
        
        // عرض الإحصائيات
        async function showStatistics() {
            try {
                const response = await fetch('api/helper_data.php?action=stats');
                const result = await response.json();

                if (result.success) {
                    const stats = result.data;
                    const statisticsSection = document.getElementById('statisticsSection');
                    const statisticsDisplay = document.getElementById('statisticsDisplay');

                    let html = `
                        <div class="form-row">
                            <div class="form-group">
                                <div class="stat-card" style="background: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;">
                                    <h3 style="color: #1976d2; margin: 0;">${stats.total_invoices}</h3>
                                    <p style="margin: 5px 0 0 0;">إجمالي الفواتير</p>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="stat-card" style="background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;">
                                    <h3 style="color: #388e3c; margin: 0;">${parseFloat(stats.total_sales).toFixed(2)}</h3>
                                    <p style="margin: 5px 0 0 0;">إجمالي المبيعات</p>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="stat-card" style="background: #fff3e0; padding: 15px; border-radius: 8px; text-align: center;">
                                    <h3 style="color: #f57c00; margin: 0;">${parseFloat(stats.monthly_sales).toFixed(2)}</h3>
                                    <p style="margin: 5px 0 0 0;">مبيعات هذا الشهر</p>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="stat-card" style="background: #fce4ec; padding: 15px; border-radius: 8px; text-align: center;">
                                    <h3 style="color: #c2185b; margin: 0;">${parseFloat(stats.daily_sales).toFixed(2)}</h3>
                                    <p style="margin: 5px 0 0 0;">مبيعات اليوم</p>
                                </div>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <h4>أكثر العملاء شراءً</h4>
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>اسم العميل</th>
                                            <th>عدد الفواتير</th>
                                            <th>إجمالي المشتريات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                    `;

                    stats.top_customers.forEach(customer => {
                        html += `
                            <tr>
                                <td>${customer.customer_name}</td>
                                <td>${customer.invoice_count}</td>
                                <td>${parseFloat(customer.total_spent).toFixed(2)}</td>
                            </tr>
                        `;
                    });

                    html += `
                                    </tbody>
                                </table>
                            </div>
                            <div class="form-group">
                                <h4>أكثر المنتجات مبيعاً</h4>
                                <table class="data-table">
                                    <thead>
                                        <tr>
                                            <th>اسم المنتج</th>
                                            <th>الكمية المباعة</th>
                                            <th>إجمالي المبيعات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                    `;

                    stats.top_products.forEach(product => {
                        html += `
                            <tr>
                                <td>${product.item_name}</td>
                                <td>${parseFloat(product.total_quantity).toFixed(2)}</td>
                                <td>${parseFloat(product.total_sales).toFixed(2)}</td>
                            </tr>
                        `;
                    });

                    html += `
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <button class="btn btn-secondary" onclick="hideStatistics()">إخفاء الإحصائيات</button>
                    `;

                    statisticsDisplay.innerHTML = html;
                    statisticsSection.style.display = 'block';
                } else {
                    showAlert('خطأ في تحميل الإحصائيات', 'danger');
                }
            } catch (error) {
                console.error('Statistics error:', error);
                showAlert('خطأ في تحميل الإحصائيات', 'danger');
            }
        }

        // إخفاء الإحصائيات
        function hideStatistics() {
            document.getElementById('statisticsSection').style.display = 'none';
        }

        // تصدير البيانات
        async function exportData() {
            try {
                const response = await fetch('api/invoice_operations.php?action=list');
                const result = await response.json();

                if (result.success && result.data.length > 0) {
                    // تحويل البيانات لتنسيق Excel
                    const exportData = result.data.map(invoice => ({
                        'رقم الفاتورة': invoice.invoice_number,
                        'التاريخ': invoice.invoice_date,
                        'اسم العميل': invoice.customer_name,
                        'العنوان': invoice.customer_address,
                        'طريقة الدفع': invoice.payment_method,
                        'المجموع الفرعي': invoice.subtotal,
                        'الخصم': invoice.discount,
                        'الرصيد السابق': invoice.previous_balance,
                        'الإجمالي النهائي': invoice.total_amount,
                        'السائق': invoice.driver,
                        'المحاسب': invoice.accountant,
                        'تاريخ الإنشاء': invoice.created_at
                    }));

                    const worksheet = XLSX.utils.json_to_sheet(exportData);
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, worksheet, 'الفواتير');

                    const today = new Date().toISOString().split('T')[0];
                    XLSX.writeFile(workbook, `invoices_${today}.xlsx`);

                    showAlert('تم تصدير البيانات بنجاح!', 'success');
                } else {
                    showAlert('لا توجد بيانات للتصدير!', 'danger');
                }
            } catch (error) {
                console.error('Export error:', error);
                showAlert('خطأ في تصدير البيانات', 'danger');
            }
        }
        
        // عرض الرسائل
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            document.body.insertBefore(alertDiv, document.body.firstChild);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 3000);
        }
        
        // الإكمال التلقائي
        let autocompleteTimeout;

        async function showAutocomplete(input, type) {
            clearTimeout(autocompleteTimeout);

            const value = input.value;
            const suggestionsDiv = document.getElementById(input.id + '_suggestions');

            if (value.length < 2) {
                suggestionsDiv.style.display = 'none';
                return;
            }

            autocompleteTimeout = setTimeout(async () => {
                try {
                    const response = await fetch(`api/helper_data.php?action=autocomplete&type=${type}&q=${encodeURIComponent(value)}`);
                    const result = await response.json();

                    if (result.success && result.data.length > 0) {
                        let html = '';
                        result.data.forEach(item => {
                            html += `<div class="autocomplete-suggestion" onclick="selectSuggestion('${input.id}', '${item.value}')">${item.value}</div>`;
                        });

                        suggestionsDiv.innerHTML = html;
                        suggestionsDiv.style.display = 'block';
                    } else {
                        suggestionsDiv.style.display = 'none';
                    }
                } catch (error) {
                    console.error('Autocomplete error:', error);
                }
            }, 300);
        }

        function selectSuggestion(inputId, value) {
            document.getElementById(inputId).value = value;
            document.getElementById(inputId + '_suggestions').style.display = 'none';
        }

        // إخفاء الاقتراحات عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.form-group')) {
                document.querySelectorAll('.autocomplete-suggestions').forEach(div => {
                    div.style.display = 'none';
                });
            }
        });

        // تحديث رقم الفاتورة التلقائي
        async function generateInvoiceNumber() {
            try {
                const response = await fetch('api/invoice_operations.php?action=new_invoice_number');
                const result = await response.json();

                if (result.success) {
                    document.getElementById('invoiceNumber').value = result.invoice_number;
                }
            } catch (error) {
                console.error('Error generating invoice number:', error);
                // رقم افتراضي
                const timestamp = Date.now().toString().slice(-6);
                document.getElementById('invoiceNumber').value = 'INV-' + timestamp;
            }
        }

        // تحميل ديون العملاء
        async function loadDebts() {
            const debtsDisplay = document.getElementById('debtsDisplay');

            try {
                debtsDisplay.innerHTML = '<p class="loading">جاري تحميل ديون العملاء...</p>';

                const response = await fetch('api/helper_data.php?action=customers');
                const result = await response.json();

                if (result.success) {
                    const debtors = result.data.filter(customer => customer.total_debt > 0);
                    displayDebts(debtors);
                } else {
                    debtsDisplay.innerHTML = '<p class="no-data">خطأ في تحميل البيانات</p>';
                }
            } catch (error) {
                console.error('Error loading debts:', error);
                debtsDisplay.innerHTML = '<p class="no-data">خطأ في الاتصال بالخادم</p>';
            }
        }

        // عرض ديون العملاء
        function displayDebts(debtors) {
            const debtsDisplay = document.getElementById('debtsDisplay');

            if (debtors.length === 0) {
                debtsDisplay.innerHTML = '<p class="no-data">لا توجد ديون للعملاء</p>';
                return;
            }

            let html = '<table class="data-table"><thead><tr>';
            html += '<th>اسم العميل</th>';
            html += '<th>العنوان</th>';
            html += '<th>رقم الهاتف</th>';
            html += '<th>إجمالي الديون</th>';
            html += '<th>إجراءات</th>';
            html += '</tr></thead><tbody>';

            let totalDebts = 0;
            debtors.forEach(customer => {
                totalDebts += parseFloat(customer.total_debt);
                html += '<tr>';
                html += `<td>${customer.name}</td>`;
                html += `<td>${customer.address || ''}</td>`;
                html += `<td>${customer.phone || ''}</td>`;
                html += `<td class="debt-amount">${parseFloat(customer.total_debt).toFixed(2)}</td>`;
                html += `<td>
                    <button class="btn btn-sm" onclick="viewCustomerInvoices('${customer.name}')">عرض الفواتير</button>
                    <button class="btn btn-success btn-sm" onclick="payDebt('${customer.name}', ${customer.total_debt})">سداد</button>
                </td>`;
                html += '</tr>';
            });

            html += '</tbody></table>';
            html += `<div class="totals-section" style="margin-top: 20px;">
                <div class="total-row final">
                    <span>إجمالي الديون:</span>
                    <span>${totalDebts.toFixed(2)}</span>
                </div>
            </div>`;

            debtsDisplay.innerHTML = html;
        }

        // فلترة ديون العملاء
        function filterDebts() {
            const searchTerm = document.getElementById('debtSearchBox').value.toLowerCase();
            const rows = document.querySelectorAll('#debtsDisplay .data-table tbody tr');

            rows.forEach(row => {
                const customerName = row.cells[0].textContent.toLowerCase();
                const address = row.cells[1].textContent.toLowerCase();

                if (customerName.includes(searchTerm) || address.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // عرض فواتير عميل معين
        async function viewCustomerInvoices(customerName) {
            try {
                const response = await fetch(`api/invoice_operations.php?action=list&customer=${encodeURIComponent(customerName)}`);
                const result = await response.json();

                if (result.success) {
                    // التبديل إلى تبويب البيانات وعرض فواتير العميل
                    showTab('data');
                    document.getElementById('customerFilter').value = customerName;
                    applyFilters();
                }
            } catch (error) {
                console.error('Error:', error);
                showAlert('خطأ في تحميل فواتير العميل', 'danger');
            }
        }

        // سداد دين عميل
        async function payDebt(customerName, debtAmount) {
            const paymentAmount = prompt(`إدخال مبلغ السداد للعميل: ${customerName}\nإجمالي الدين: ${debtAmount}`, debtAmount);

            if (paymentAmount && !isNaN(paymentAmount) && parseFloat(paymentAmount) > 0) {
                try {
                    const response = await fetch('api/helper_data.php?action=pay_debt', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            customer_name: customerName,
                            payment_amount: parseFloat(paymentAmount)
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        showAlert('تم تسجيل السداد بنجاح', 'success');
                        loadDebts(); // إعادة تحميل البيانات
                    } else {
                        showAlert('خطأ في تسجيل السداد', 'danger');
                    }
                } catch (error) {
                    console.error('Error:', error);
                    showAlert('خطأ في الاتصال بالخادم', 'danger');
                }
            }
        }

        // تحسين تجربة المستخدم
        function enhanceUserExperience() {
            // إضافة اختصارات لوحة المفاتيح
            document.addEventListener('keydown', function(e) {
                // Ctrl + S لحفظ الفاتورة
                if (e.ctrlKey && e.key === 's') {
                    e.preventDefault();
                    if (document.getElementById('invoiceTab').classList.contains('active')) {
                        document.getElementById('saveInvoiceBtn').click();
                    }
                }

                // Ctrl + N لمسح البيانات
                if (e.ctrlKey && e.key === 'n') {
                    e.preventDefault();
                    if (document.getElementById('invoiceTab').classList.contains('active')) {
                        clearFormManually();
                    }
                }

                // Enter في حقل الكمية أو السعر لحساب الإجمالي
                if (e.key === 'Enter' && (e.target.classList.contains('item-quantity') || e.target.classList.contains('item-price'))) {
                    calculateRowTotal(e.target);
                }
            });

            // تحسين التنقل بين الحقول
            const inputs = document.querySelectorAll('input, select, textarea');
            inputs.forEach((input, index) => {
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        const nextInput = inputs[index + 1];
                        if (nextInput) {
                            nextInput.focus();
                        }
                    }
                });
            });
        }

        // تهيئة النظام
        function initializeSystem() {
            // تعيين التاريخ الحالي
            document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];

            // توليد رقم فاتورة تلقائي
            generateInvoiceNumber();

            // تحسين تجربة المستخدم
            enhanceUserExperience();

            // إخفاء المبلغ المتبقي في البداية
            document.getElementById('remainingAmountRow').style.display = 'none';

            // تحميل البيانات الأولية
            if (document.getElementById('dataTab').classList.contains('active')) {
                loadData();
            } else if (document.getElementById('debtsTab').classList.contains('active')) {
                loadDebts();
            }
        }

        // تشغيل النظام عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', initializeSystem);
    </script>
</body>
</html>