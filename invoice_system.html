<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الفواتير</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        body {
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }
        
        .tab {
            flex: 1;
            padding: 15px;
            background: #e9ecef;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .tab.active {
            background: white;
            color: #667eea;
        }
        
        .tab:hover {
            background: #dee2e6;
        }
        
        .tab-content {
            display: none;
            padding: 30px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .form-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #495057;
            border-bottom: 2px solid #667eea;
            padding-bottom: 5px;
        }
        
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
            flex-wrap: wrap;
        }
        
        .form-group {
            flex: 1;
            min-width: 200px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #495057;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        
        .items-table th, .items-table td {
            border: 1px solid #dee2e6;
            padding: 10px;
            text-align: center;
        }
        
        .items-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        
        .items-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .totals-section {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 16px;
        }
        
        .total-row.final {
            font-weight: bold;
            font-size: 18px;
            color: #667eea;
            border-top: 2px solid #667eea;
            padding-top: 10px;
        }
        
        .file-input {
            margin-bottom: 20px;
            padding: 15px;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .data-table th, .data-table td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: right;
        }
        
        .data-table th {
            background: #667eea;
            color: white;
            font-weight: bold;
        }
        
        .data-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .search-box {
            width: 100%;
            padding: 12px;
            margin-bottom: 20px;
            border: 2px solid #667eea;
            border-radius: 5px;
            font-size: 16px;
        }
        
        #fileInput {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>نظام إدارة الفواتير</h1>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="showTab('invoice')">إدخال فاتورة جديدة</button>
            <button class="tab" onclick="showTab('data')">عرض البيانات</button>
        </div>
        
        <!-- صفحة إدخال الفاتورة -->
        <div id="invoiceTab" class="tab-content active">
            <div class="file-input">
                <label for="fileInput">اختر ملف Excel لحفظ البيانات (أول مرة فقط):</label>
                <input type="file" id="fileInput" accept=".xlsx,.xls" onchange="handleFileSelect()">
                <button class="btn btn-secondary" onclick="document.getElementById('fileInput').click()">اختيار ملف</button>
                <span id="fileName" style="margin-right: 10px; color: #667eea; font-weight: bold;"></span>
            </div>
            
            <form id="invoiceForm">
                <!-- معلومات الفاتورة الأساسية -->
                <div class="form-section">
                    <div class="section-title">معلومات الفاتورة</div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>رقم الفاتورة:</label>
                            <input type="text" id="invoiceNumber" required>
                        </div>
                        <div class="form-group">
                            <label>تاريخ الفاتورة:</label>
                            <input type="date" id="invoiceDate" required>
                        </div>
                        <div class="form-group">
                            <label>اسم العميل:</label>
                            <input type="text" id="customerName" required>
                        </div>
                        <div class="form-group">
                            <label>طريقة الدفع:</label>
                            <select id="paymentMethod">
                                <option value="نقدي">نقدي</option>
                                <option value="شيك">شيك</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- الأصناف -->
                <div class="form-section">
                    <div class="section-title">الأصناف</div>
                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>التسلسل</th>
                                <th>اسم الصنف</th>
                                <th>الكمية</th>
                                <th>السعر</th>
                                <th>الإجمالي</th>
                                <th>إجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="itemsTableBody">
                            <tr>
                                <td>1</td>
                                <td><input type="text" class="item-name" placeholder="اسم الصنف"></td>
                                <td><input type="number" class="item-quantity" placeholder="الكمية" onchange="calculateRowTotal(this)"></td>
                                <td><input type="number" step="0.01" class="item-price" placeholder="السعر" onchange="calculateRowTotal(this)"></td>
                                <td class="item-total">0</td>
                                <td><button type="button" class="btn btn-danger" onclick="removeRow(this)">حذف</button></td>
                            </tr>
                        </tbody>
                    </table>
                    <button type="button" class="btn" onclick="addRow()" style="margin-top: 10px;">إضافة صنف</button>
                </div>
                
                <!-- الإجماليات -->
                <div class="form-section">
                    <div class="section-title">الإجماليات</div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>إجمالي الفاتورة:</label>
                            <input type="number" id="totalAmount" step="0.01" readonly>
                        </div>
                        <div class="form-group">
                            <label>الخصم:</label>
                            <input type="number" id="discount" step="0.01" value="0" onchange="calculateFinalTotal()">
                        </div>
                        <div class="form-group">
                            <label>الرصيد السابق:</label>
                            <input type="number" id="previousBalance" step="0.01" value="0" onchange="calculateFinalTotal()">
                        </div>
                    </div>
                    <div class="totals-section">
                        <div class="total-row final">
                            <span>صافي المبلغ المطلوب:</span>
                            <span id="finalTotal">0</span>
                        </div>
                    </div>
                </div>
                
                <!-- معلومات إضافية -->
                <div class="form-section">
                    <div class="section-title">معلومات إضافية</div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>عنوان العميل:</label>
                            <textarea id="customerAddress" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label>السائق:</label>
                            <input type="text" id="driver">
                        </div>
                        <div class="form-group">
                            <label>المحاسب:</label>
                            <input type="text" id="accountant">
                        </div>
                    </div>
                </div>
                
                <button type="submit" class="btn btn-success" style="width: 100%; font-size: 18px;">حفظ الفاتورة</button>
            </form>
        </div>
        
        <!-- صفحة عرض البيانات -->
        <div id="dataTab" class="tab-content">
            <div class="form-section">
                <div class="section-title">البحث في البيانات</div>
                <input type="text" id="searchBox" class="search-box" placeholder="ابحث في الفواتير..." onkeyup="filterData()">
                <button class="btn" onclick="loadData()">تحديث البيانات</button>
                <button class="btn btn-secondary" onclick="exportData()" style="margin-right: 10px;">تصدير البيانات</button>
            </div>
            
            <div id="dataDisplay">
                <p>لا توجد بيانات للعرض. قم بتحميل ملف Excel أو إنشاء فاتورة جديدة.</p>
            </div>
        </div>
    </div>
    
    <script>
        let currentFile = null;
        let invoiceData = [];
        let rowCounter = 1;
        
        // تبديل التبويبات
        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            document.getElementById(tabName + 'Tab').classList.add('active');
            event.target.classList.add('active');
            
            if (tabName === 'data') {
                loadData();
            }
        }
        
        // اختيار ملف Excel
        function handleFileSelect() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (file) {
                currentFile = file;
                document.getElementById('fileName').textContent = file.name;
                loadExistingData(file);
            }
        }
        
        // تحميل البيانات الموجودة من الملف
        function loadExistingData(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const workbook = XLSX.read(e.target.result, { type: 'binary' });
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const data = XLSX.utils.sheet_to_json(worksheet);
                    invoiceData = data;
                    showAlert('تم تحميل البيانات الموجودة بنجاح!', 'success');
                } catch (error) {
                    console.log('ملف جديد - لا توجد بيانات سابقة');
                    invoiceData = [];
                }
            };
            reader.readAsBinaryString(file);
        }
        
        // إضافة صف جديد للأصناف
        function addRow() {
            rowCounter++;
            const tableBody = document.getElementById('itemsTableBody');
            const newRow = document.createElement('tr');
            newRow.innerHTML = `
                <td>${rowCounter}</td>
                <td><input type="text" class="item-name" placeholder="اسم الصنف"></td>
                <td><input type="number" class="item-quantity" placeholder="الكمية" onchange="calculateRowTotal(this)"></td>
                <td><input type="number" step="0.01" class="item-price" placeholder="السعر" onchange="calculateRowTotal(this)"></td>
                <td class="item-total">0</td>
                <td><button type="button" class="btn btn-danger" onclick="removeRow(this)">حذف</button></td>
            `;
            tableBody.appendChild(newRow);
        }
        
        // حذف صف
        function removeRow(button) {
            const row = button.closest('tr');
            row.remove();
            calculateTotal();
            updateRowNumbers();
        }
        
        // تحديث أرقام الصفوف
        function updateRowNumbers() {
            const rows = document.querySelectorAll('#itemsTableBody tr');
            rows.forEach((row, index) => {
                row.firstElementChild.textContent = index + 1;
            });
            rowCounter = rows.length;
        }
        
        // حساب إجمالي الصف
        function calculateRowTotal(input) {
            const row = input.closest('tr');
            const quantity = row.querySelector('.item-quantity').value || 0;
            const price = row.querySelector('.item-price').value || 0;
            const total = quantity * price;
            
            row.querySelector('.item-total').textContent = total.toFixed(2);
            calculateTotal();
        }
        
        // حساب الإجمالي العام
        function calculateTotal() {
            let total = 0;
            document.querySelectorAll('.item-total').forEach(cell => {
                total += parseFloat(cell.textContent) || 0;
            });
            
            document.getElementById('totalAmount').value = total.toFixed(2);
            calculateFinalTotal();
        }
        
        // حساب الإجمالي النهائي
        function calculateFinalTotal() {
            const total = parseFloat(document.getElementById('totalAmount').value) || 0;
            const discount = parseFloat(document.getElementById('discount').value) || 0;
            const previousBalance = parseFloat(document.getElementById('previousBalance').value) || 0;
            
            const finalTotal = total - discount + previousBalance;
            document.getElementById('finalTotal').textContent = finalTotal.toFixed(2);
        }
        
        // حفظ الفاتورة
        document.getElementById('invoiceForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (!currentFile) {
                showAlert('يرجى اختيار ملف Excel أولاً!', 'danger');
                return;
            }
            
            // جمع بيانات الأصناف
            const items = [];
            document.querySelectorAll('#itemsTableBody tr').forEach(row => {
                const name = row.querySelector('.item-name').value;
                const quantity = row.querySelector('.item-quantity').value;
                const price = row.querySelector('.item-price').value;
                const total = row.querySelector('.item-total').textContent;
                
                if (name && quantity && price) {
                    items.push({
                        name: name,
                        quantity: parseFloat(quantity),
                        price: parseFloat(price),
                        total: parseFloat(total)
                    });
                }
            });
            
            // إنشاء كائن الفاتورة
            const invoice = {
                رقم_الفاتورة: document.getElementById('invoiceNumber').value,
                تاريخ_الفاتورة: document.getElementById('invoiceDate').value,
                اسم_العميل: document.getElementById('customerName').value,
                طريقة_الدفع: document.getElementById('paymentMethod').value,
                الأصناف: JSON.stringify(items),
                إجمالي_الفاتورة: parseFloat(document.getElementById('totalAmount').value),
                الخصم: parseFloat(document.getElementById('discount').value) || 0,
                الرصيد_السابق: parseFloat(document.getElementById('previousBalance').value) || 0,
                صافي_المبلغ: parseFloat(document.getElementById('finalTotal').textContent),
                عنوان_العميل: document.getElementById('customerAddress').value,
                السائق: document.getElementById('driver').value,
                المحاسب: document.getElementById('accountant').value,
                تاريخ_الحفظ: new Date().toISOString().split('T')[0]
            };
            
            // إضافة الفاتورة للبيانات
            invoiceData.push(invoice);
            
            // حفظ في ملف Excel
            saveToExcel();
            
            // إعادة تعيين النموذج
            resetForm();
            
            showAlert('تم حفظ الفاتورة بنجاح!', 'success');
        });
        
        // حفظ البيانات في ملف Excel
        function saveToExcel() {
            const worksheet = XLSX.utils.json_to_sheet(invoiceData);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'الفواتير');
            
            // حفظ الملف
            const fileName = currentFile.name || 'invoices.xlsx';
            XLSX.writeFile(workbook, fileName);
        }
        
        // إعادة تعيين النموذج
        function resetForm() {
            document.getElementById('invoiceForm').reset();
            document.getElementById('itemsTableBody').innerHTML = `
                <tr>
                    <td>1</td>
                    <td><input type="text" class="item-name" placeholder="اسم الصنف"></td>
                    <td><input type="number" class="item-quantity" placeholder="الكمية" onchange="calculateRowTotal(this)"></td>
                    <td><input type="number" step="0.01" class="item-price" placeholder="السعر" onchange="calculateRowTotal(this)"></td>
                    <td class="item-total">0</td>
                    <td><button type="button" class="btn btn-danger" onclick="removeRow(this)">حذف</button></td>
                </tr>
            `;
            rowCounter = 1;
            document.getElementById('totalAmount').value = '';
            document.getElementById('finalTotal').textContent = '0';
        }
        
        // تحميل البيانات لعرضها
        function loadData() {
            const dataDisplay = document.getElementById('dataDisplay');
            
            if (invoiceData.length === 0) {
                dataDisplay.innerHTML = '<p>لا توجد بيانات للعرض. قم بتحميل ملف Excel أو إنشاء فاتورة جديدة.</p>';
                return;
            }
            
            let html = '<table class="data-table"><thead><tr>';
            html += '<th>رقم الفاتورة</th>';
            html += '<th>التاريخ</th>';
            html += '<th>اسم العميل</th>';
            html += '<th>طريقة الدفع</th>';
            html += '<th>إجمالي الفاتورة</th>';
            html += '<th>الخصم</th>';
            html += '<th>صافي المبلغ</th>';
            html += '<th>السائق</th>';
            html += '<th>المحاسب</th>';
            html += '</tr></thead><tbody>';
            
            invoiceData.forEach(invoice => {
                html += '<tr>';
                html += `<td>${invoice.رقم_الفاتورة}</td>`;
                html += `<td>${invoice.تاريخ_الفاتورة}</td>`;
                html += `<td>${invoice.اسم_العميل}</td>`;
                html += `<td>${invoice.طريقة_الدفع}</td>`;
                html += `<td>${invoice.إجمالي_الفاتورة}</td>`;
                html += `<td>${invoice.الخصم}</td>`;
                html += `<td>${invoice.صافي_المبلغ}</td>`;
                html += `<td>${invoice.السائق || ''}</td>`;
                html += `<td>${invoice.المحاسب || ''}</td>`;
                html += '</tr>';
            });
            
            html += '</tbody></table>';
            dataDisplay.innerHTML = html;
        }
        
        // البحث في البيانات
        function filterData() {
            const searchTerm = document.getElementById('searchBox').value.toLowerCase();
            const filteredData = invoiceData.filter(invoice => 
                Object.values(invoice).some(value => 
                    value.toString().toLowerCase().includes(searchTerm)
                )
            );
            
            // عرض البيانات المفلترة
            displayFilteredData(filteredData);
        }
        
        function displayFilteredData(data) {
            const dataDisplay = document.getElementById('dataDisplay');
            
            if (data.length === 0) {
                dataDisplay.innerHTML = '<p>لا توجد نتائج مطابقة لبحثك.</p>';
                return;
            }
            
            let html = '<table class="data-table"><thead><tr>';
            html += '<th>رقم الفاتورة</th>';
            html += '<th>التاريخ</th>';
            html += '<th>اسم العميل</th>';
            html += '<th>طريقة الدفع</th>';
            html += '<th>إجمالي الفاتورة</th>';
            html += '<th>الخصم</th>';
            html += '<th>صافي المبلغ</th>';
            html += '<th>السائق</th>';
            html += '<th>المحاسب</th>';
            html += '</tr></thead><tbody>';
            
            data.forEach(invoice => {
                html += '<tr>';
                html += `<td>${invoice.رقم_الفاتورة}</td>`;
                html += `<td>${invoice.تاريخ_الفاتورة}</td>`;
                html += `<td>${invoice.اسم_العميل}</td>`;
                html += `<td>${invoice.طريقة_الدفع}</td>`;
                html += `<td>${invoice.إجمالي_الفاتورة}</td>`;
                html += `<td>${invoice.الخصم}</td>`;
                html += `<td>${invoice.صافي_المبلغ}</td>`;
                html += `<td>${invoice.السائق || ''}</td>`;
                html += `<td>${invoice.المحاسب || ''}</td>`;
                html += '</tr>';
            });
            
            html += '</tbody></table>';
            dataDisplay.innerHTML = html;
        }
        
        // تصدير البيانات
        function exportData() {
            if (invoiceData.length === 0) {
                showAlert('لا توجد بيانات للتصدير!', 'danger');
                return;
            }
            
            const worksheet = XLSX.utils.json_to_sheet(invoiceData);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, 'الفواتير');
            
            const today = new Date().toISOString().split('T')[0];
            XLSX.writeFile(workbook, `invoices_${today}.xlsx`);
            
            showAlert('تم تصدير البيانات بنجاح!', 'success');
        }
        
        // عرض الرسائل
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            document.body.insertBefore(alertDiv, document.body.firstChild);
            
            setTimeout(() => {
                alertDiv.remove();
            }, 3000);
        }
        
        // تعيين التاريخ الحالي
        document.getElementById('invoiceDate').value = new Date().toISOString().split('T')[0];
    </script>
</body>
</html>