-- ملف تحديث قاعدة البيانات للمستخدمين الحاليين
-- تشغيل هذا الملف إذا كان لديك نظام قديم وتريد إضافة الحقول الجديدة

USE invoice_system;

-- إض<PERSON><PERSON>ة الحقول الجديدة لجدول الفواتير
ALTER TABLE invoices 
ADD COLUMN IF NOT EXISTS remaining_amount DECIMAL(10,2) DEFAULT 0.00 AFTER total_amount,
ADD COLUMN IF NOT EXISTS cartons_count INT DEFAULT 0 AFTER remaining_amount,
ADD COLUMN IF NOT EXISTS weight_tons DECIMAL(8,3) DEFAULT 0.000 AFTER cartons_count,
ADD COLUMN IF NOT EXISTS sales_rep VARCHAR(255) AFTER driver,
ADD COLUMN IF NOT EXISTS supplier VARCHAR(255) AFTER sales_rep;

-- تحديث طرق الدفع
ALTER TABLE invoices 
MODIFY COLUMN payment_method ENUM('نقداً', 'أجل') DEFAULT 'نقداً';

-- <PERSON><PERSON><PERSON><PERSON><PERSON> حقل الديون لجدول العملاء
ALTER TABLE customers 
ADD COLUMN IF NOT EXISTS total_debt DECIMAL(10,2) DEFAULT 0.00 AFTER email;

-- إنشاء جدول إعدادات النظام إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- إدراج رقم الفاتورة الأولي
INSERT INTO system_settings (setting_key, setting_value) VALUES 
('last_invoice_number', '0')
ON DUPLICATE KEY UPDATE setting_value = setting_value;

-- تحديث الديون الحالية للعملاء (للفواتير الموجودة بطريقة دفع أجل)
UPDATE customers c 
SET total_debt = (
    SELECT COALESCE(SUM(i.total_amount), 0) 
    FROM invoices i 
    WHERE i.customer_name = c.name 
    AND i.payment_method = 'أجل'
);

-- تحديث المبلغ المتبقي للفواتير الموجودة
UPDATE invoices 
SET remaining_amount = CASE 
    WHEN payment_method = 'أجل' THEN total_amount 
    ELSE 0 
END
WHERE remaining_amount IS NULL OR remaining_amount = 0;

-- إضافة فهارس للحقول الجديدة
CREATE INDEX IF NOT EXISTS idx_sales_rep ON invoices(sales_rep);
CREATE INDEX IF NOT EXISTS idx_supplier ON invoices(supplier);
CREATE INDEX IF NOT EXISTS idx_remaining_amount ON invoices(remaining_amount);

-- تحديث طرق الدفع القديمة
UPDATE invoices SET payment_method = 'نقداً' WHERE payment_method = 'نقدي';
UPDATE invoices SET payment_method = 'أجل' WHERE payment_method = 'شيك';

COMMIT;
