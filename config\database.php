<?php
/**
 * إعدادات قاعدة البيانات
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'invoice_system');
define('DB_USER', 'root'); // غير هذا إلى اسم المستخدم الخاص بك
define('DB_PASS', ''); // غير هذا إلى كلمة المرور الخاصة بك
define('DB_CHARSET', 'utf8mb4');

/**
 * كلاس الاتصال بقاعدة البيانات
 */
class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    private $pdo;

    /**
     * الحصول على اتصال قاعدة البيانات
     */
    public function getConnection() {
        $this->pdo = null;

        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
        } catch(PDOException $exception) {
            echo "خطأ في الاتصال: " . $exception->getMessage();
        }

        return $this->pdo;
    }

    /**
     * إغلاق الاتصال
     */
    public function closeConnection() {
        $this->pdo = null;
    }
}

/**
 * دالة مساعدة للحصول على اتصال قاعدة البيانات
 */
function getDBConnection() {
    $database = new Database();
    return $database->getConnection();
}

/**
 * دالة لتنفيذ استعلام وإرجاع النتائج
 */
function executeQuery($sql, $params = []) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch(PDOException $e) {
        error_log("Database Error: " . $e->getMessage());
        return false;
    }
}

/**
 * دالة للحصول على جميع النتائج
 */
function fetchAll($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetchAll() : [];
}

/**
 * دالة للحصول على نتيجة واحدة
 */
function fetchOne($sql, $params = []) {
    $stmt = executeQuery($sql, $params);
    return $stmt ? $stmt->fetch() : null;
}

/**
 * دالة لإدراج بيانات وإرجاع ID الجديد
 */
function insertAndGetId($sql, $params = []) {
    try {
        $pdo = getDBConnection();
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $pdo->lastInsertId();
    } catch(PDOException $e) {
        error_log("Insert Error: " . $e->getMessage());
        return false;
    }
}

/**
 * دالة لبدء معاملة
 */
function beginTransaction() {
    $pdo = getDBConnection();
    return $pdo->beginTransaction();
}

/**
 * دالة لتأكيد المعاملة
 */
function commit() {
    $pdo = getDBConnection();
    return $pdo->commit();
}

/**
 * دالة لإلغاء المعاملة
 */
function rollback() {
    $pdo = getDBConnection();
    return $pdo->rollback();
}
?>
