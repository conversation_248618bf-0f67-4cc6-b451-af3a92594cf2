<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

require_once '../config/database.php';

// التعامل مع طلبات OPTIONS (CORS)
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'customers':
            getCustomers();
            break;
        case 'products':
            getProducts();
            break;
        case 'stats':
            getStatistics();
            break;
        case 'autocomplete':
            getAutocomplete();
            break;
        default:
            http_response_code(400);
            echo json_encode(['error' => 'إجراء غير صحيح']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في الخادم: ' . $e->getMessage()]);
}

/**
 * الحصول على قائمة العملاء
 */
function getCustomers() {
    $search = $_GET['search'] ?? '';
    
    if (!empty($search)) {
        $sql = "SELECT * FROM customers WHERE name LIKE ? ORDER BY name LIMIT 20";
        $customers = fetchAll($sql, ['%' . $search . '%']);
    } else {
        $sql = "SELECT * FROM customers ORDER BY name LIMIT 50";
        $customers = fetchAll($sql);
    }

    echo json_encode([
        'success' => true,
        'data' => $customers
    ]);
}

/**
 * الحصول على قائمة المنتجات
 */
function getProducts() {
    $search = $_GET['search'] ?? '';
    
    if (!empty($search)) {
        $sql = "SELECT * FROM products WHERE name LIKE ? ORDER BY name LIMIT 20";
        $products = fetchAll($sql, ['%' . $search . '%']);
    } else {
        $sql = "SELECT * FROM products ORDER BY name LIMIT 50";
        $products = fetchAll($sql);
    }

    echo json_encode([
        'success' => true,
        'data' => $products
    ]);
}

/**
 * الحصول على إحصائيات النظام
 */
function getStatistics() {
    $stats = [];

    // إجمالي عدد الفواتير
    $totalInvoices = fetchOne("SELECT COUNT(*) as count FROM invoices")['count'];
    $stats['total_invoices'] = $totalInvoices;

    // إجمالي المبيعات
    $totalSales = fetchOne("SELECT SUM(total_amount) as total FROM invoices")['total'] ?? 0;
    $stats['total_sales'] = $totalSales;

    // إجمالي المبيعات هذا الشهر
    $monthlySales = fetchOne("SELECT SUM(total_amount) as total FROM invoices WHERE MONTH(invoice_date) = MONTH(CURRENT_DATE()) AND YEAR(invoice_date) = YEAR(CURRENT_DATE())")['total'] ?? 0;
    $stats['monthly_sales'] = $monthlySales;

    // إجمالي المبيعات اليوم
    $dailySales = fetchOne("SELECT SUM(total_amount) as total FROM invoices WHERE DATE(invoice_date) = CURRENT_DATE()")['total'] ?? 0;
    $stats['daily_sales'] = $dailySales;

    // عدد العملاء
    $totalCustomers = fetchOne("SELECT COUNT(*) as count FROM customers")['count'];
    $stats['total_customers'] = $totalCustomers;

    // أكثر العملاء شراءً
    $topCustomers = fetchAll("SELECT customer_name, COUNT(*) as invoice_count, SUM(total_amount) as total_spent 
                              FROM invoices 
                              GROUP BY customer_name 
                              ORDER BY total_spent DESC 
                              LIMIT 5");
    $stats['top_customers'] = $topCustomers;

    // أكثر المنتجات مبيعاً
    $topProducts = fetchAll("SELECT item_name, SUM(quantity) as total_quantity, SUM(total_price) as total_sales 
                             FROM invoice_items 
                             GROUP BY item_name 
                             ORDER BY total_quantity DESC 
                             LIMIT 5");
    $stats['top_products'] = $topProducts;

    // مبيعات آخر 7 أيام
    $weeklyData = fetchAll("SELECT DATE(invoice_date) as date, SUM(total_amount) as daily_total 
                            FROM invoices 
                            WHERE invoice_date >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAY) 
                            GROUP BY DATE(invoice_date) 
                            ORDER BY date");
    $stats['weekly_data'] = $weeklyData;

    // توزيع طرق الدفع
    $paymentMethods = fetchAll("SELECT payment_method, COUNT(*) as count, SUM(total_amount) as total 
                                FROM invoices 
                                GROUP BY payment_method");
    $stats['payment_methods'] = $paymentMethods;

    echo json_encode([
        'success' => true,
        'data' => $stats
    ]);
}

/**
 * الحصول على بيانات الإكمال التلقائي
 */
function getAutocomplete() {
    $type = $_GET['type'] ?? '';
    $search = $_GET['q'] ?? '';
    
    if (empty($type) || empty($search)) {
        echo json_encode(['success' => true, 'data' => []]);
        return;
    }

    $results = [];

    switch ($type) {
        case 'customer':
            $results = fetchAll("SELECT DISTINCT customer_name as value FROM invoices WHERE customer_name LIKE ? ORDER BY customer_name LIMIT 10", ['%' . $search . '%']);
            break;
        case 'product':
            $results = fetchAll("SELECT DISTINCT item_name as value FROM invoice_items WHERE item_name LIKE ? ORDER BY item_name LIMIT 10", ['%' . $search . '%']);
            break;
        case 'driver':
            $results = fetchAll("SELECT DISTINCT driver as value FROM invoices WHERE driver LIKE ? AND driver != '' ORDER BY driver LIMIT 10", ['%' . $search . '%']);
            break;
        case 'accountant':
            $results = fetchAll("SELECT DISTINCT accountant as value FROM invoices WHERE accountant LIKE ? AND accountant != '' ORDER BY accountant LIMIT 10", ['%' . $search . '%']);
            break;
    }

    echo json_encode([
        'success' => true,
        'data' => $results
    ]);
}
?>
